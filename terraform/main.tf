terraform {
  # backend "pg" {
  #   conn_str             = ""
  #   schema_name          = ""
  #   skip_schema_creation = false
  # }
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    # http = {
    #   source  = "hashicorp/http"
    #   version = "3.4.3"
    # }
    local = {
      source  = "hashicorp/local"
      version = "2.5.1"
    }
    kubectl = {
      source  = "gavi<PERSON><PERSON><PERSON>/kubectl"
      version = ">= 1.7.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

data "digitalocean_kubernetes_cluster" "cluster" {
  name = "k8s-ops"
}

provider "kubernetes" {
  host    = data.digitalocean_kubernetes_cluster.cluster.endpoint
  token   = data.digitalocean_kubernetes_cluster.cluster.kube_config[0].token
  cluster_ca_certificate = base64decode(
    data.digitalocean_kubernetes_cluster.cluster.kube_config[0].cluster_ca_certificate
  )
}

# Data source to get all namespaces in the cluster
data "kubernetes_all_namespaces" "all" {}

# Local value to get filtered namespaces for use in other resources
locals {
  filtered_namespaces = [
    for ns in data.kubernetes_all_namespaces.all.namespaces : ns
    if !contains([
      "default",
      "kube-node-lease",
      "kube-public",
      "kube-system"
    ], ns)
  ]
}

# Get deployments from each filtered namespace using kubectl provider
data "kubectl_path_documents" "deployments" {
  for_each = toset(local.filtered_namespaces)
  pattern  = "${path.module}/deployments-${each.value}.yaml"

  # This is a workaround - we'll use null_resource to generate the files
  depends_on = [null_resource.get_deployments]
}

# Use null_resource to execute kubectl commands and save deployment info
resource "null_resource" "get_deployments" {
  for_each = toset(local.filtered_namespaces)

  provisioner "local-exec" {
    command = <<-EOT
      kubectl get deployments -n ${each.value} -o yaml > ${path.module}/deployments-${each.value}.yaml || echo "apiVersion: v1\nkind: List\nitems: []" > ${path.module}/deployments-${each.value}.yaml
    EOT
  }

  # Trigger re-execution when namespaces change
  triggers = {
    namespace = each.value
  }
}

# Output deployments from all filtered namespaces
output "namespace_deployments" {
  description = "Deployments in each user namespace"
  value = {
    for ns in local.filtered_namespaces : ns => {
      namespace = ns
      deployment_files = try(data.kubectl_path_documents.deployments[ns].documents, [])
    }
  }
}

# Create a simple script to generate import commands for existing deployments
resource "local_file" "import_script" {
  filename = "${path.module}/import_deployments.sh"
  content = <<-EOT
#!/bin/bash
set -e

echo "Generating Terraform import commands for existing deployments..."

# Get all deployments from filtered namespaces
for namespace in ${join(" ", local.filtered_namespaces)}; do
  echo "Checking namespace: $namespace"

  # Get deployments in this namespace
  deployments=$(kubectl get deployments -n $namespace -o jsonpath='{.items[*].metadata.name}' 2>/dev/null || echo "")

  if [ ! -z "$deployments" ]; then
    for deployment in $deployments; do
      echo "Found deployment: $deployment in namespace: $namespace"

      # Generate import command
      echo "terraform import 'kubernetes_deployment_v1.managed_deployments[\"$namespace-$deployment\"]' $namespace/$deployment"

      # Generate deployment YAML for reference
      kubectl get deployment $deployment -n $namespace -o yaml > deployments-$namespace-$deployment.yaml
    done
  fi
done

echo "Import commands generated. Run the terraform import commands above to import existing deployments."
EOT

  file_permission = "0755"
}

# Output script location
output "import_script_location" {
  description = "Location of the import script for existing deployments"
  value = local_file.import_script.filename
}

# Create placeholder resources for deployments that can be imported
# You'll need to manually define these based on your actual deployments
# Example structure - uncomment and modify as needed:

# resource "kubernetes_deployment_v1" "managed_deployments" {
#   for_each = {
#     # Define your deployments statically here, e.g.:
#     # "namespace1-app1" = {
#     #   name = "app1"
#     #   namespace = "namespace1"
#     #   image = "nginx:latest"
#     #   replicas = 1
#     # }
#   }
#
#   metadata {
#     name      = each.value.name
#     namespace = each.value.namespace
#   }
#
#   spec {
#     replicas = each.value.replicas
#
#     selector {
#       match_labels = {
#         app = each.value.name
#       }
#     }
#
#     template {
#       metadata {
#         labels = {
#           app = each.value.name
#         }
#       }
#
#       spec {
#         container {
#           name  = each.value.name
#           image = each.value.image
#         }
#       }
#     }
#   }
#
#   lifecycle {
#     ignore_changes = [
#       metadata[0].resource_version,
#       metadata[0].uid,
#       spec[0].template[0].metadata[0].annotations,
#     ]
#   }
# }

# Output the imported deployments
output "imported_deployments" {
  description = "Imported deployments as Terraform managed resources"
  value = {
    for key, deployment in kubernetes_deployment_v1.imported_deployments : key => {
      name      = deployment.metadata[0].name
      namespace = deployment.metadata[0].namespace
      replicas  = deployment.spec[0].replicas
    }
  }
}


terraform {
  # backend "pg" {
  #   conn_str             = ""
  #   schema_name          = ""
  #   skip_schema_creation = false
  # }
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    # http = {
    #   source  = "hashicorp/http"
    #   version = "3.4.3"
    # }
    local = {
      source  = "hashicorp/local"
      version = "2.5.1"
    }
    kubectl = {
      source  = "gavi<PERSON><PERSON><PERSON>/kubectl"
      version = ">= 1.7.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

data "digitalocean_kubernetes_cluster" "cluster" {
  name = "k8s-ops"
}

provider "kubernetes" {
  host    = data.digitalocean_kubernetes_cluster.cluster.endpoint
  token   = data.digitalocean_kubernetes_cluster.cluster.kube_config[0].token
  cluster_ca_certificate = base64decode(
    data.digitalocean_kubernetes_cluster.cluster.kube_config[0].cluster_ca_certificate
  )
}

# Data source to get all namespaces in the cluster
data "kubernetes_all_namespaces" "all" {}

# Local value to get filtered namespaces for use in other resources
locals {
  filtered_namespaces = [
    for ns in data.kubernetes_all_namespaces.all.namespaces : ns
    if !contains([
      "default",
      "kube-node-lease",
      "kube-public",
      "kube-system"
    ], ns)
  ]
}

# Get deployments from each filtered namespace using kubectl provider
data "kubectl_path_documents" "deployments" {
  for_each = toset(local.filtered_namespaces)
  pattern  = "${path.module}/deployments-${each.value}.yaml"

  # This is a workaround - we'll use null_resource to generate the files
  depends_on = [null_resource.get_deployments]
}

# Use null_resource to execute kubectl commands and save deployment info
resource "null_resource" "get_deployments" {
  for_each = toset(local.filtered_namespaces)

  provisioner "local-exec" {
    command = <<-EOT
      kubectl get deployments -n ${each.value} -o yaml > ${path.module}/deployments-${each.value}.yaml || echo "apiVersion: v1\nkind: List\nitems: []" > ${path.module}/deployments-${each.value}.yaml
    EOT
  }

  # Trigger re-execution when namespaces change
  triggers = {
    namespace = each.value
  }
}

# Output deployments from all filtered namespaces
output "namespace_deployments" {
  description = "Deployments in each user namespace"
  value = {
    for ns in local.filtered_namespaces : ns => {
      namespace = ns
      deployment_files = try(data.kubectl_path_documents.deployments[ns].documents, [])
    }
  }
}

# Local value to extract deployment information from kubectl documents
locals {
  deployment_info = {
    for deployment in flatten([
      for ns in local.filtered_namespaces : [
        for doc in try(data.kubectl_path_documents.deployments[ns].documents, []) : {
          key = "${yamldecode(doc).metadata.namespace}-${yamldecode(doc).metadata.name}"
          namespace = yamldecode(doc).metadata.namespace
          name = yamldecode(doc).metadata.name
          yaml_content = doc
          parsed = yamldecode(doc)
        }
        if try(yamldecode(doc).kind, "") == "Deployment"
      ]
    ]) : deployment.key => deployment
  }
}

# Import existing deployments as managed resources
resource "kubernetes_deployment_v1" "imported_deployments" {
  for_each = local.deployment_info

  metadata {
    name      = each.value.parsed.metadata.name
    namespace = each.value.parsed.metadata.namespace
    labels    = try(each.value.parsed.metadata.labels, {})
  }

  spec {
    replicas = try(each.value.parsed.spec.replicas, 1)

    selector {
      match_labels = each.value.parsed.spec.selector.matchLabels
    }

    template {
      metadata {
        labels = each.value.parsed.spec.template.metadata.labels
      }

      spec {
        dynamic "container" {
          for_each = each.value.parsed.spec.template.spec.containers
          content {
            name  = container.value.name
            image = container.value.image

            dynamic "port" {
              for_each = try(container.value.ports, [])
              content {
                container_port = port.value.containerPort
                protocol       = try(port.value.protocol, "TCP")
              }
            }

            dynamic "env" {
              for_each = try(container.value.env, [])
              content {
                name  = env.value.name
                value = try(env.value.value, null)

                dynamic "value_from" {
                  for_each = try(env.value.valueFrom, null) != null ? [env.value.valueFrom] : []
                  content {
                    dynamic "config_map_key_ref" {
                      for_each = try(value_from.value.configMapKeyRef, null) != null ? [value_from.value.configMapKeyRef] : []
                      content {
                        name = config_map_key_ref.value.name
                        key  = config_map_key_ref.value.key
                      }
                    }
                    dynamic "secret_key_ref" {
                      for_each = try(value_from.value.secretKeyRef, null) != null ? [value_from.value.secretKeyRef] : []
                      content {
                        name = secret_key_ref.value.name
                        key  = secret_key_ref.value.key
                      }
                    }
                  }
                }
              }
            }

            dynamic "resources" {
              for_each = try(container.value.resources, null) != null ? [container.value.resources] : []
              content {
                dynamic "limits" {
                  for_each = try(resources.value.limits, null) != null ? [resources.value.limits] : []
                  content {
                    cpu    = try(limits.value.cpu, null)
                    memory = try(limits.value.memory, null)
                  }
                }
                dynamic "requests" {
                  for_each = try(resources.value.requests, null) != null ? [resources.value.requests] : []
                  content {
                    cpu    = try(requests.value.cpu, null)
                    memory = try(requests.value.memory, null)
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  lifecycle {
    ignore_changes = [
      metadata[0].resource_version,
      metadata[0].uid,
      spec[0].template[0].metadata[0].annotations,
    ]
  }
}

# Output the imported deployments
output "imported_deployments" {
  description = "Imported deployments as Terraform managed resources"
  value = {
    for key, deployment in kubernetes_deployment_v1.imported_deployments : key => {
      name      = deployment.metadata[0].name
      namespace = deployment.metadata[0].namespace
      replicas  = deployment.spec[0].replicas
    }
  }
}

